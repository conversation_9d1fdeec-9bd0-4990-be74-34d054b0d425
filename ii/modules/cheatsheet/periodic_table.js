// List of rows
const elements = [
    [
        { name: 'Hydrogen', symbol: 'H', number: 1, weight: 1.01, type: 'nonmetal' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: 'Helium', symbol: 'He', number: 2, weight: 4.00, type: 'noblegas' },
    ],
    [
        { name: 'Lithium', symbol: 'Li', number: 3, weight: 6.94, type: 'metal' },
        { name: 'Beryllium', symbol: 'Be', number: 4, weight: 9.01, type: 'metal' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: 'Boron', symbol: 'B', number: 5, weight: 10.81, type: 'nonmetal' },
        { name: 'Carbon', symbol: 'C', number: 6, weight: 12.01, type: 'nonmetal' },
        { name: 'Nitrogen', symbol: 'N', number: 7, weight: 14.01, type: 'nonmetal' },
        { name: 'Oxygen', symbol: 'O', number: 8, weight: 16, type: 'nonmetal' },
        { name: 'Fluorine', symbol: 'F', number: 9, weight: 19, type: 'nonmetal' },
        { name: 'Neon', symbol: 'Ne', number: 10, weight: 20.18, type: 'noblegas' },


    ],
    [
        { name: 'Sodium', symbol: 'Na', number: 11, weight: 22.99, type: 'metal' },
        { name: 'Magnesium', symbol: 'Mg', number: 12, weight: 24.31, type: 'metal' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: 'Aluminum', symbol: 'Al', number: 13, weight: 26.98, type: 'metal' },
        { name: 'Silicon', symbol: 'Si', number: 14, weight: 28.09, type: 'nonmetal' },
        { name: 'Phosphorus', symbol: 'P', number: 15, weight: 30.97, type: 'nonmetal' },
        { name: 'Sulfur', symbol: 'S', number: 16, weight: 32.07, type: 'nonmetal' },
        { name: 'Chlorine', symbol: 'Cl', number: 17, weight: 35.45, type: 'nonmetal' },
        { name: 'Argon', symbol: 'Ar', number: 18, weight: 39.95, type: 'noblegas' },
    ],
    [
        { name: 'Potassium', symbol: 'K', number: 19, weight: 39.098, type: 'metal' },
        { name: 'Calcium', symbol: 'Ca', number: 20, weight: 40.078, type: 'metal' },
        { name: 'Scandium', symbol: 'Sc', number: 21, weight: 44.956, type: 'metal' },
        { name: 'Titanium', symbol: 'Ti', number: 22, weight: 47.87, type: 'metal' },
        { name: 'Vanadium', symbol: 'V', number: 23, weight: 50.94, type: 'metal' },
        { name: 'Chromium', symbol: 'Cr', number: 24, weight: 52, type: 'metal'/*, icon: 'chromium-browser'*/ },
        { name: 'Manganese', symbol: 'Mn', number: 25, weight: 54.94, type: 'metal' },
        { name: 'Iron', symbol: 'Fe', number: 26, weight: 55.85, type: 'metal' },
        { name: 'Cobalt', symbol: 'Co', number: 27, weight: 58.93, type: 'metal' },
        { name: 'Nickel', symbol: 'Ni', number: 28, weight: 58.69, type: 'metal' },
        { name: 'Copper', symbol: 'Cu', number: 29, weight: 63.55, type: 'metal' },
        { name: 'Zinc', symbol: 'Zn', number: 30, weight: 65.38, type: 'metal' },
        { name: 'Gallium', symbol: 'Ga', number: 31, weight: 69.72, type: 'metal' },
        { name: 'Germanium', symbol: 'Ge', number: 32, weight: 72.63, type: 'metal' },
        { name: 'Arsenic', symbol: 'As', number: 33, weight: 74.92, type: 'nonmetal' },
        { name: 'Selenium', symbol: 'Se', number: 34, weight: 78.96, type: 'nonmetal' },
        { name: 'Bromine', symbol: 'Br', number: 35, weight: 79.904, type: 'nonmetal' },
        { name: 'Krypton', symbol: 'Kr', number: 36, weight: 83.8, type: 'noblegas' },
    ],
    [
        { name: 'Rubidium', symbol: 'Rb', number: 37, weight: 85.47, type: 'metal' },
        { name: 'Strontium', symbol: 'Sr', number: 38, weight: 87.62, type: 'metal' },
        { name: 'Yttrium', symbol: 'Y', number: 39, weight: 88.91, type: 'metal' },
        { name: 'Zirconium', symbol: 'Zr', number: 40, weight: 91.22, type: 'metal' },
        { name: 'Niobium', symbol: 'Nb', number: 41, weight: 92.91, type: 'metal' },
        { name: 'Molybdenum', symbol: 'Mo', number: 42, weight: 95.94, type: 'metal' },
        { name: 'Technetium', symbol: 'Tc', number: 43, weight: 98, type: 'metal' },
        { name: 'Ruthenium', symbol: 'Ru', number: 44, weight: 101.07, type: 'metal' },
        { name: 'Rhodium', symbol: 'Rh', number: 45, weight: 102.91, type: 'metal' },
        { name: 'Palladium', symbol: 'Pd', number: 46, weight: 106.42, type: 'metal' },
        { name: 'Silver', symbol: 'Ag', number: 47, weight: 107.87, type: 'metal' },
        { name: 'Cadmium', symbol: 'Cd', number: 48, weight: 112.41, type: 'metal' },
        { name: 'Indium', symbol: 'In', number: 49, weight: 114.82, type: 'metal' },
        { name: 'Tin', symbol: 'Sn', number: 50, weight: 118.71, type: 'metal' },
        { name: 'Antimony', symbol: 'Sb', number: 51, weight: 121.76, type: 'metal' },
        { name: 'Tellurium', symbol: 'Te', number: 52, weight: 127.6, type: 'nonmetal' },
        { name: 'Iodine', symbol: 'I', number: 53, weight: 126.9, type: 'nonmetal' },
        { name: 'Xenon', symbol: 'Xe', number: 54, weight: 131.29, type: 'noblegas' },
    ],
    [
        { name: 'Cesium', symbol: 'Cs', number: 55, weight: 132.91, type: 'metal' },
        { name: 'Barium', symbol: 'Ba', number: 56, weight: 137.33, type: 'metal' },
        { name: 'Lanthanum', symbol: 'La', number: 57, weight: 138.91, type: 'lanthanum' },
        { name: 'Hafnium', symbol: 'Hf', number: 72, weight: 178.49, type: 'metal' },
        { name: 'Tantalum', symbol: 'Ta', number: 73, weight: 180.95, type: 'metal' },
        { name: 'Tungsten', symbol: 'W', number: 74, weight: 183.84, type: 'metal' },
        { name: 'Rhenium', symbol: 'Re', number: 75, weight: 186.21, type: 'metal' },
        { name: 'Osmium', symbol: 'Os', number: 76, weight: 190.23, type: 'metal' },
        { name: 'Iridium', symbol: 'Ir', number: 77, weight: 192.22, type: 'metal' },
        { name: 'Platinum', symbol: 'Pt', number: 78, weight: 195.09, type: 'metal' },
        { name: 'Gold', symbol: 'Au', number: 79, weight: 196.97, type: 'metal' },
        { name: 'Mercury', symbol: 'Hg', number: 80, weight: 200.59, type: 'metal' },
        { name: 'Thallium', symbol: 'Tl', number: 81, weight: 204.38, type: 'metal' },
        { name: 'Lead', symbol: 'Pb', number: 82, weight: 207.2, type: 'metal' },
        { name: 'Bismuth', symbol: 'Bi', number: 83, weight: 208.98, type: 'metal' },
        { name: 'Polonium', symbol: 'Po', number: 84, weight: 209, type: 'metal' },
        { name: 'Astatine', symbol: 'At', number: 85, weight: 210, type: 'nonmetal' },
        { name: 'Radon', symbol: 'Rn', number: 86, weight: 222, type: 'noblegas' },
    ],
    [
        { name: 'Francium', symbol: 'Fr', number: 87, weight: 223, type: 'metal' },
        { name: 'Radium', symbol: 'Ra', number: 88, weight: 226, type: 'metal' },
        { name: 'Actinium', symbol: 'Ac', number: 89, weight: 227, type: 'actinium' },
        { name: 'Rutherfordium', symbol: 'Rf', number: 104, weight: 267, type: 'metal' },
        { name: 'Dubnium', symbol: 'Db', number: 105, weight: 268, type: 'metal' },
        { name: 'Seaborgium', symbol: 'Sg', number: 106, weight: 271, type: 'metal' },
        { name: 'Bohrium', symbol: 'Bh', number: 107, weight: 272, type: 'metal' },
        { name: 'Hassium', symbol: 'Hs', number: 108, weight: 277, type: 'metal' },
        { name: 'Meitnerium', symbol: 'Mt', number: 109, weight: 278, type: 'metal' },
        { name: 'Darmstadtium', symbol: 'Ds', number: 110, weight: 281, type: 'metal' },
        { name: 'Roentgenium', symbol: 'Rg', number: 111, weight: 280, type: 'metal' },
        { name: 'Copernicium', symbol: 'Cn', number: 112, weight: 285, type: 'metal' },
        { name: 'Nihonium', symbol: 'Nh', number: 113, weight: 286, type: 'metal' },
        { name: 'Flerovium', symbol: 'Fl', number: 114, weight: 289, type: 'metal' },
        { name: 'Moscovium', symbol: 'Mc', number: 115, weight: 290, type: 'metal' },
        { name: 'Livermorium', symbol: 'Lv', number: 116, weight: 293, type: 'metal' },
        { name: 'Tennessine', symbol: 'Ts', number: 117, weight: 294, type: 'metal' },
        { name: 'Oganesson', symbol: 'Og', number: 118, weight: 294, type: 'noblegas' },
    ],
]

const series = [
    [
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: 'Cerium', symbol: 'Ce', number: 58, weight: 140.12, type: 'lanthanum' },
        { name: 'Praseodymium', symbol: 'Pr', number: 59, weight: 140.91, type: 'lanthanum' },
        { name: 'Neodymium', symbol: 'Nd', number: 60, weight: 144.24, type: 'lanthanum' },
        { name: 'Promethium', symbol: 'Pm', number: 61, weight: 145, type: 'lanthanum' },
        { name: 'Samarium', symbol: 'Sm', number: 62, weight: 150.36, type: 'lanthanum' },
        { name: 'Europium', symbol: 'Eu', number: 63, weight: 151.96, type: 'lanthanum' },
        { name: 'Gadolinium', symbol: 'Gd', number: 64, weight: 157.25, type: 'lanthanum' },
        { name: 'Terbium', symbol: 'Tb', number: 65, weight: 158.93, type: 'lanthanum' },
        { name: 'Dysprosium', symbol: 'Dy', number: 66, weight: 162.5, type: 'lanthanum' },
        { name: 'Holmium', symbol: 'Ho', number: 67, weight: 164.93, type: 'lanthanum' },
        { name: 'Erbium', symbol: 'Er', number: 68, weight: 167.26, type: 'lanthanum' },
        { name: 'Thulium', symbol: 'Tm', number: 69, weight: 168.93, type: 'lanthanum' },
        { name: 'Ytterbium', symbol: 'Yb', number: 70, weight: 173.04, type: 'lanthanum' },
        { name: 'Lutetium', symbol: 'Lu', number: 71, weight: 174.97, type: 'lanthanum' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
    ],
    [
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
        { name: 'Thorium', symbol: 'Th', number: 90, weight: 232.04, type: 'actinium' },
        { name: 'Protactinium', symbol: 'Pa', number: 91, weight: 231.04, type: 'actinium' },
        { name: 'Uranium', symbol: 'U', number: 92, weight: 238.03, type: 'actinium' },
        { name: 'Neptunium', symbol: 'Np', number: 93, weight: 237, type: 'actinium' },
        { name: 'Plutonium', symbol: 'Pu', number: 94, weight: 244, type: 'actinium' },
        { name: 'Americium', symbol: 'Am', number: 95, weight: 243, type: 'actinium' },
        { name: 'Curium', symbol: 'Cm', number: 96, weight: 247, type: 'actinium' },
        { name: 'Berkelium', symbol: 'Bk', number: 97, weight: 247, type: 'actinium' },
        { name: 'Californium', symbol: 'Cf', number: 98, weight: 251, type: 'actinium' },
        { name: 'Einsteinium', symbol: 'Es', number: 99, weight: 252, type: 'actinium' },
        { name: 'Fermium', symbol: 'Fm', number: 100, weight: 257, type: 'actinium' },
        { name: 'Mendelevium', symbol: 'Md', number: 101, weight: 258, type: 'actinium' },
        { name: 'Nobelium', symbol: 'No', number: 102, weight: 259, type: 'actinium' },
        { name: 'Lawrencium', symbol: 'Lr', number: 103, weight: 262, type: 'actinium' },
        { name: '', symbol: '', number: -1, weight: 0, type: 'empty' },
    ],
];

const niceTypes = {
    'metal': "Metal",
    'nonmetal': "Nonmetal",
    'noblegas': "Noble gas",
    'lanthanum': "Lanthanum",
    'actinium': "Actinium"
}
