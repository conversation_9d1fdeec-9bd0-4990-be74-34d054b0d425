import QtQuick
import QtQuick.Layouts
import Quickshell
import qs.modules.common
import qs.modules.common.functions
import qs.modules.common.widgets

Rectangle {
    implicitHeight: 1
    color: Appearance.colors.colOutline
    Layout.fillWidth: true
    Layout.leftMargin: -Appearance.rounding.large
    Layout.rightMargin: -Appearance.rounding.large
    Layout.topMargin: -8
    Layout.bottomMargin: -8
}
