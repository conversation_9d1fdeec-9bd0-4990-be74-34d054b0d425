{"Launch": "Avvia", "Columns": "Colonne", "Save": "<PERSON><PERSON>", "Temperature: %1": "Temperatura: %1", "Night Light | Right-click to toggle Auto mode": "Modalità notte", "Silent": "Silenzia", "To Do": "Promemoria", "Action": "<PERSON><PERSON><PERSON>", "Search the web": "Cerca sul web", "Workspace": "Spazio di lavoro", "Desktop": "Scrivania", "Settings": "Impostazioni", "Math result": "Risultato", "Calendar": "Calendario", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Cancel": "Can<PERSON><PERSON>", "Search": "Cerca", "Battery": "Batteria", "Weather": "Meteo", "Brightness": "Luminosità", "Clear": "Can<PERSON><PERSON>", "No notifications": "Nessuna notifica", "No media": "Non in riproduzione", "Add task": "Aggiungi promemoria", "Run command": "<PERSON><PERSON><PERSON><PERSON> comando", "Game mode": "Modalità gioco", "Reload Hyprland & Quickshell": "Riavvia Hyprland e Quickshell", "Task description": "<PERSON><PERSON> promemoria", "%1 | Right-click to configure": "%1", "Done": "Completati", "Keep system awake": "<PERSON><PERSON><PERSON> schermo attivo", "Search, calculate or run": "Cerca, calcola o esegui", "Copy": "Copia", "Rows": "<PERSON><PERSON><PERSON>", "Session": "Sessione", "Notifications": "Notifiche", "Unfinished": "Da completare", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Nothing here!": "N<PERSON>un promemoria", "Mo": "<PERSON>", "Tu": "Ma", "We": "Me", "Th": "Gi", "Fr": "Ve", "Sa": "Sa", "Su": "Do", "Edit config": "<PERSON>i config.", "Center title": "<PERSON><PERSON>rato", "Elements": "Elementi", "Color picker": "Selettore colore", "Title bar": "Barra del titolo", "Sleep": "<PERSON><PERSON><PERSON><PERSON>", "Transparency": "Trasparenza", "Bluetooth": "Bluetooth", "UV Index": "Indice UV", "Bar": "Barr<PERSON>", "Format": "Formato", "Select output device": "Seleziona dispositivo di output", "Pressure": "Pressione", "Volume": "Volume", "Volume mixer": "Mixer volume", "Interface": "Interfaccia", "Workspaces": "Spazi di lavoro", "Dark": "<PERSON><PERSON>", "%1 notifications": "%1 notifiche", "Reboot": "Riavvia", "No": "No", "Wind": "Vento", "Humidity": "Umidità", "Select Language": "Seleziona lingua", "Wallpaper": "Sfondo", "Copy code": "Copia codice", "Allow NSFW": "Mostra NSFW", "Colors & Wallpaper": "Sfondo e stile", "Shutdown": "Spegni", "Decorations & Effects": "Decorazioni e effetti", "Translation goes here...": "Traduzione", "Polling interval (ms)": "Intervallo di polling (ms)", "System prompt": "Prompt di sistema", "Base URL": "URL base", "Always show numbers": "Mostra numeri", "Wallpaper parallax": "<PERSON><PERSON><PERSON> para<PERSON> sfondo", "Plain rectangle": "Semplice", "illogical-impulse Welcome": "Benvenuto su illogical-impulse", "Local only": "Solo locale", "Dark/Light toggle": "Modalità chiaro/scuro", "Screen snip": "Cattura schermo", "Style & wallpaper": "Sfondo e stile", "Show app icons": "Mostra icone app", "Useless buttons": "<PERSON><PERSON><PERSON> in<PERSON>", "Scale (%)": "Dimensione (%)", "Show background": "Mostra sfondo", "Intelligence": "AI", "Enable": "Abilita", "Borderless": "<PERSON><PERSON> bordi", "Random: Konachan": "Casuale: <PERSON><PERSON><PERSON>", "Yes": "Sì", "Documentation": "Documentazione", "Unknown Artist": "<PERSON><PERSON>", "Help & Support": "Aiuto e supporto", "Report a Bug": "<PERSON><PERSON><PERSON> un <PERSON>", "Sunset": "Tramonto", "Weeb": "Anime", "Shell windows": "Finestre", "Workspaces shown": "Numero spazi di lavoro", "Screenshot tool": "Cattura schermo", "Enter text to translate...": "Inserisci il testo qui", "Unknown Album": "Album sconosciuto", "Hug": "Stondato", "Scroll to change brightness": "Scorri per cambiare luminosità", "Privacy Policy": "Privacy policy", "12h AM/PM": "12 ore (AM/PM)", "Material palette": "Tavolozza dei colori", "No audio source": "Nessuna sorgente audio", "Download": "Scarica", "Prefixes": "<PERSON><PERSON><PERSON>", "Show next time": "Mostra al prossimo avvio", "Lock": "Blocca", "Scroll to change volume": "Scorri per cambiare volume", "Unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Jump to current month": "Torna al mese corrente", "Cloudflare WARP (*******)": "Cloudflare WARP (*******)", "Terminal": "Terminale", "About": "Informazioni", "Precipitation": "Precipitazioni", "Keybinds": "<PERSON><PERSON><PERSON>", "Select input device": "Seleziona dispositivo di input", "When not fullscreen": "Tranne a schermo intero", "Unknown Title": "<PERSON><PERSON>", "Task Manager": "Gestione attività", "System": "Sistema", "Choose file": "Scegli file", "Pinned on startup": "<PERSON><PERSON> sullo schermo", "Policies": "<PERSON><PERSON><PERSON>", "View Markdown source": "<PERSON><PERSON>", "Sunrise": "Alba", "Configuration": "Configurazione", "Overview": "Panoramica", "Translator": "<PERSON><PERSON><PERSON><PERSON>", "Finished tasks will go here": "N<PERSON>un promemoria", "Mic toggle": "Microfono", "Light": "Chiaro", "Advanced": "Avanzate", "Web search": "Cerca sul web", "12h am/pm": "12 ore (am/pm)", "Services": "<PERSON><PERSON><PERSON>", "Donate": "Supporta", "Resources": "Risorse", "Float": "Fluttuante", "Fake screen rounding": "<PERSON><PERSON> curvi schermo", "Hibernate": "Iberna", "Visibility": "Visibilità", "Delete": "Elimina", "Style": "Stile", "Page %1": "Pagina %1", "Keyboard toggle": "Tastiera virtuale", "Buttons": "<PERSON><PERSON><PERSON><PERSON>", "24h": "24 ore", "Time": "<PERSON>a", "Clipboard": "<PERSON><PERSON><PERSON><PERSON>", "or": "o", "Edit": "Modifica", "Emojis": "<PERSON><PERSON><PERSON>", "Shell & utilities": "App e shell", "Reboot to firmware settings": "Riavvia alle impostazioni firmware", "No API key set for %1": "Chiave API non impostata per %1", "Disable NSFW content": "Nascondi contenuti NSFW", "Closet": "Nascosto", "Depends on sidebars": "Abilita per barre laterali", "Invalid model. Supported: \n```": "Modello non valido. Supportati: \n```", "Type /key to get started with online models\nCtrl+O to expand the sidebar\nCtrl+P to detach sidebar into a window": "Usa /key per utilizzare i modelli online\nCtrl+O per espandere\nCtrl+P per separare in finestra", "Not visible to model": "Non visible al modello", "Local Ollama model | %1": "Modello Ollama locale | %1", "Open file link": "Apri link al file", "Waiting for response...": "In attesa di risposta...", "Cheat sheet": "Prontuario", "Allow NSFW content": "Mostra contenuti NSFW", "%1 characters": "%1 caratteri", "Model set to %1": "Modello impostato su %1", "Be patient...": "Attendi...", "User agent (for services that require it)": "User agent (usato dai servizi)", "Current API endpoint: %1\nSet it with %2mode PROVIDER": "Endpoint API: %1\nUsa %2mode per cambiarlo", "For desktop wallpapers | Good quality": "Per sfondi del desktop | Buona qualità", "For storing API keys and other sensitive information": "Gestione credenziali e informazioni sensibili", "Your package manager is running": "Il package manager è in esecuzione", "Provider set to": "Provider impostato su", "Color generation": "Tavolozza dei colori", "Temperature must be between 0 and 2": "Il valore della temperatura deve essere fra 0 e 2", "Earbang protection": "Protezione udito", "Alternatively use /dark, /light, /img in the launcher": "Oppure usa /dark, /light, /img nel launcher", "Change any time later with /dark, /light, /img in the launcher": "Oppure usa /dark, /light, /img nel launcher", "Current model: %1\nSet it with %2model MODEL": "Modello attuale: %1\nUsa %2model per cambiarlo", "Waifus only | Excellent quality, limited quantity": "Solo waifu | Qualità eccellente, quantità limitata", "Save to Downloads": "Salva in Scaricati", "Thinking": "Ragionando", "On-screen display": "Popup di sistema", "%1   •   %2 tasks": "%1   •   %2 promemoria", "Qt apps": "Applicazioni Qt", "The popular one | Best quantity, but quality can vary wildly": "Il più usato | Quantità elevata, ma la qualità potrebbe variare totalmente", "Set the system prompt for the model.": "Imposta il prompt di sistema per il modello.", "Markdown test": "Test Markdown", "Prevents abrupt increments and restricts volume limit": "Impedice aumenti improvvisi del volume e ne imposta un limite.", "Preferred wallpaper zoom (%)": "Zoom sfondo (%)", "Depends on workspace": "Abilita per spazi di lavoro", "Note: turning off can hurt readability": "Nota: disatti<PERSON><PERSON> potrebbe ridurre la leggibilità", "Pick wallpaper image on your system": "Seleziona un'immagine nel tuo sistema", "Invalid API provider. Supported: \n-": "Provider API non valido. Supportati: \n-", "The hentai one | Great quantity, a lot of NSFW, quality varies wildly": "Hentai | Quantità elevata, NSFW, la qualità potrebbe variare totalmente", "Saved to %1": "Salvato in %1", "Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.": "Imposta la temperatura (casualità) del modello. Il valore va da 0 a 2 per Gemini, e da 0 a 1 per gli altri modelli. Il valore predefinito è 0.5.", "Close": "<PERSON><PERSON>", "Might look ass. Unsupported.": "Potrebbe fare schifo. Non supportato.", "API key set for %1": "Chiave API impostata per %1", "Temperature\nChange with /temp VALUE": "Temperatura\nUsa /temp per cambiarla", "%1 does not require an API key": "%1 non richiede una chiave API", "Online models disallowed\n\nControlled by `policies.ai` config option": "Modelli online disattivati\n\nDisattiva l'opzione \"Solo locale\"", "Set the current API provider": "Imposta il provider API", "Total token count\nInput: %1\nOutput: %2": "Numero token totali\nInput: %1\nOutput: %2", "EasyEffects | Right-click to configure": "EasyEffects", "Random SFW Anime wallpaper from Konachan\nImage is saved to ~/Pictures/Wallpapers": "Sfondo anime SFW casuale da Konachan\nL'immagine verrà salvata in ~/Immagini/Wallpapers", "Hover to reveal": "Mostra col passaggio del mouse", "Arrow keys to navigate, Enter to select\nEsc or click anywhere to cancel": "Usa i tasti freccia per navigare, Invio per selezionare\nEsc o clicca qualsiasi punto per uscire", "Save chat to %1": "Salva chat in %1", "Choose model": "Seleziona modello", "No API key\nSet it with /key YOUR_API_KEY": "Nessuna chiave API\nUsa /key per impostarla", "API key:\n\n```txt\n%1\n```": "Chiave API:\n\n```txt\n%1\n```", "Use Levenshtein distance-based algorithm instead of fuzzy": "Usa algoritmo di Levenshtein al posto di fuzzy", "Download complete": "Download completato", "Tip: Hide icons and always show numbers for\nthe classic illogical-impulse experience": "Suggerimento: Nascondi le icone e mostra i numeri se vuoi\nun'esperienza fedele all'originale", "Usage": "Istruzioni", "Set API key": "Imposta chiave API", "Networking": "Rete", "Volume limit": "Limite volume", "Temperature set to %1": "Temperatura impostata a %1", "Large images | God tier quality, no NSFW.": "Immagini grandi | Qualità perfetta, no NSFW.", "Shell & utilities theming must also be enabled": "\"App e shell\" deve essere abilitato", "API key is set\nChange with /key YOUR_API_KEY": "Chiave API impostata\nUsa /key per cambiarla", "All-rounder | Good quality, decent quantity": "Versatile | Qualità buona, quantità discreta", "Large language models": "Intelligenza artificiale", "Could be better if you make a ton of typos,\nbut results can be weird and might not work with acronyms\n(e.g. \"GIMP\" might not give you the paint program)": "<PERSON><PERSON><PERSON> aiutare in caso di refusi,\nma i risultati potrebbero non essere accurati\n(es. \"GIMP\" potrebbe non restituire l'omonimo programma)", "Automatic suspend": "Sospensione automatica", "Max allowed increase": "Aumento massimo consentito", "Please charge!\nAutomatic suspend triggers at %1": "Ricarica la batteria!\nSospensione automatica in %1", "Weather Service": "<PERSON><PERSON><PERSON>", "The current system prompt is\n\n---\n\n%1": "Il prompt di sistema attuale è\n\n---\n\n%1", "%1 Safe Storage": "Portachiavi di %1", "Load prompt from %1": "Carica prompt da %1", "That didn't work. Tips:\n- Check your tags and NSFW settings\n- If you don't have a tag in mind, type a page number": "<PERSON><PERSON>un risultato. Suggerimenti:\n- Controlla i tag e le impostazioni NSFW\n- Se non hai un tag in mente, inserici il numero di pagina", "Load chat": "Carica chat", "**Pricing**: free. Data used for training.\n\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key": "**Prezzo**: gratuito. I tuoi dati vengono usati per training.\n\n**Istruzioni**: Accedi al tuo account Google, abilita i permessi richiesti da AI Studio, torna indietro e seleziona \"Get API key\"", "Online via %1 | %2's model": "Online tramite %1 | Modello di %2", "Get the next page of results": "Ottieni la pagina successiva dei risultati", "Unknown function call: %1": "Funzione sconosciuta: %1", "Cannot find a GPS service. Using the fallback method instead.": "Servizio GPS non disponibile. Verrà utilizzata la città preimpostata.", "Registration failed. Please inspect manually with the <tt>warp-cli</tt> command": "Registrazione fallita. Verifica l'errore manualmente col comando <tt>warp-cli</tt>", "Code saved to file": "Co<PERSON> salvato", "Low warning": "<PERSON><PERSON> basso", "Clear the current list of images": "Elimina la lista corrente di immagini", "Invalid arguments. Must provide `key` and `value`.": "Argomenti non validi. Il comando richiede `key` e `value`.", "Connection failed. Please inspect manually with the <tt>warp-cli</tt> command": "Connessione fallita. Verifica l'errore manualmente col comando <tt>warp-cli</tt>", "Unknown command:": "<PERSON><PERSON><PERSON>:", "Message the model... \"%1\" for commands": "\"%1\" per mostrare i comandi", "Load chat from %1": "Carica chat da %1", "**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\n\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key": "**Prezzo**: gratuito. Le policy di trattamento dei dati potrebbero variare in base alle impostazioni del tuo account OpenRouter.\n\n**Istruzioni**: Accedi al tuo account OpenRouter, vai nella sezione \"Keys\" dal menu in alto, clicca \"Create API Key\"", "Enter tags, or \"%1\" for commands": "\"%1\" per mostrare i comandi", "Show regions of potential interest": "Mostra regioni d'interesse", "Critical warning": "<PERSON><PERSON> critico", "Go to source (%1)": "<PERSON>ai alla fonte (%1)", "Automatically suspends the system when battery is low": "Sospende automaticamente il sistema quando il livello della batteria è basso", "Clean stuff | Excellent quality, no NSFW": "Roba pulita | Qualità eccellente, no NSFW", ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!": ". Note per Zerochan:\n- Devi inserire un colore\n- Imposta il tuo nome utente di zerochan nell'opzione `sidebar.booru.zerochan.username`. Potresti [venire bannato se non lo fai](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!", "Critically low battery": "<PERSON><PERSON><PERSON> scarica", "Loaded the following system prompt\n\n---\n\n%1": "Il seguente prompt di sistema è stato caricato\n\n---\n\n%1", "Suspend at": "<PERSON><PERSON><PERSON><PERSON> al", "Such regions could be images or parts of the screen that have some containment.\nMight not always be accurate.\nThis is done with an image processing algorithm run locally and no AI is used.": "Queste regioni potrebbero essere immagini o parti di schermo contenute.\nPotrebbe non essere preciso.\nViene utilizzato un algoritmo di image processing in locale, non viene usata AI.", "Clear chat history": "Elimina cronologia chat", "Low battery": "Batteria quasi scarica", "Save chat": "<PERSON><PERSON> chat", "Switched to search mode. Continue with the user's request.": "Modalità ricerca attiva. Continua con la richiesta dell'utente.", "Number show delay when pressing Super (ms)": "Mostra numeri premendo Super dopo (ms)", "Drag or click a region • LMB: Copy • RMB: Edit": "Trascina o clicca una regione • LMB: Copia • RMB: Modifica", "Consider plugging in your device": "<PERSON><PERSON>i il tuo dispositivo alla rete elettrica", "%1 queries pending": "%1 ricerche in sospeso", "<i>No further instruction provided</i>": "<i>Nessun'altra istruzione fornita</i>", "There might be a download in progress": "Potrebbe esserci un download in corso", "Approve": "<PERSON><PERSON><PERSON><PERSON>", "Invalid arguments. Must provide `command`.": "Argomenti non validi. Il comando richiede `command`.", "Reject": "<PERSON><PERSON><PERSON><PERSON>", "Thought": "<PERSON><PERSON><PERSON>", "Performance Profile toggle": "Profilo prestazioni", "Command rejected by user": "Comando rifiutato dall'utente", "Up %1": "Tempo di attività: %1", "Overall appearance": "Aspetto", "Online | Google's model\nGoogle's state-of-the-art multipurpose model that excels at coding and complex reasoning tasks.": "Online | Modello di Google\nModello multiuso che eccelle in scrittura di codice e compiti di ragionamento complessi.", "Usage: %1tool TOOL_NAME": "Istruzioni: %1tool TOOL_NAME", "Set the tool to use for the model.": "Imposta lo strumento da usare con questo modello.", "Online | Google's model\nFast, can perform searches for up-to-date information": "Online | Modello di Google\nVeloce, effettua ricerche con informazioni attuali", "Invalid tool. Supported tools:\n- %1": "Strumento non valido. Strumenti supportati:\n- %1", "Usage: %1load CHAT_NAME": "Istruzioni: %1load CHAT_NAME", "Current tool: %1\nSet it with %2tool TOOL": "Strumento attuale: %1\nUsa %2tool per cambiarlo", "Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "Online | Modello di Google\nModello Gemini 2.5 Flash ottimizzato per efficienza e throughput elevato.", "To set an API key, pass it with the %4 command\n\nTo view the key, pass \"get\" with the command<br/>\n\n### For %1:\n\n**Link**: %2\n\n%3": "Per impostare una chiave API, utilizzala come argomento del comando %4\n\nPer vedere la chiave corrente, utilizza come argomento \"get\"<br/>\n\n### Per %1:\n\n**Link**: %2\n\n%3", "Tool set to: %1": "Strumento impostato su: %1", "Disable tools": "Disattiva strumenti", "Usage: %1save CHAT_NAME": "Istruzioni: %1save CHAT_NAME", "Online | Google's model\nNewer model that's slower than its predecessor but should deliver higher quality answers": "Online | Modello di Google\nPiù lento del suo predecessore ma restituisce risposte di qualità maggiore", "Tint icons": "Icone monocromatiche", "Tray": "Area di notifica", "Tint app icons": "Icone monocromatiche", "Sidebars": "Barre laterali", "Keep right sidebar loaded": "<PERSON><PERSON>i barra laterale destra in memoria", "Automatically hide": "Nascondi automaticamente", "Pause": "Pausa", "Stopwatch": "Cronometro", "🌿 Long break: %1 minutes": "🌿 Pausa lunga: %1 minuti", "Reset": "R<PERSON><PERSON><PERSON>", "Resume": "<PERSON><PERSON><PERSON><PERSON>", "Break": "Pausa breve", "🔴 Focus: %1 minutes": "🔴 Focus: %1 minuti", "☕ Break: %1 minutes": "☕ Pausa breve: %1 minuti", "Incorrect password": "Password errata", "Start": "Avvia", "Lap": "Parziale", "Enter password": "Inser<PERSON>ci password", "Long break": "<PERSON><PERSON> lunga", "Anime boorus": "Boorus anime", "High": "Alto", "To Do:": "Promemoria:", "Charging:": "In carica:", "... and %1 more": "... e altri %1", "No pending tasks": "<PERSON><PERSON><PERSON>", "System uptime:": "Tempo di attività:", "Total:": "Totale:", "Medium": "Medio", "Time to full:": "Tempo di ricarica:", "Discharging:": "Consumo:", "Free:": "Disponibile:", "Fully charged": "Carica completa", "Time to empty:": "Tempo rim<PERSON>nte:", "Load:": "Carico:", "Used:": "In uso:", "Low": "<PERSON><PERSON>", "Vertical": "Verticale", "Horizontal": "Orizzontale", "Hi there! First things first...": "Ciao! Per cominciare...", "Welcome app": "App di benvenuto", "Enjoy! You can reopen the welcome app any time with <tt>Super+Shift+Alt+/</tt>. To open the settings app, hit <tt>Super+I</tt>": "Puoi riaprire la schermata di benvenuto con <tt>Super+Shift+Alt+/</tt>. Per aprire le impostazioni, usa <tt>Super+I</tt>", "Attach a file. Only works with Gemini.": "Allega un file. Funziona solo su Gemini.", "Always": "Sempre", "Place at the bottom/right": "Posiziona in basso/a destra", "**Pricing**: Free tier available with limited rates. See https://docs.github.com/en/billing/concepts/product-billing/github-models\n\n**Instructions**: Generate a GitHub personal access token with Models permission, then set as API key here\n\n**Note**: To use this you will have to set the temperature parameter to 1": "**Prezzo**: Piano gratuito disponibile con utilizzo limitato. Più info su https://docs.github.com/en/billing/concepts/product-billing/github-models\n\n**Istruzioni**: Genera un personal access token su GitHub con i permessi \"Models\" e impostalo come chiave API\n\n**Nota**: Per utilizzare questo modello imposta la temperatura a 1", "Refreshing (manually triggered)": "Aggiornamento in corso (richiesto manualmente)", "Shell conflicts killer": "Avviso conflitti", "Kill conflicting programs?": "Terminare programmi in conflitto?", "Conflicts with the shell's notification implementation": "In conflitto con il sistema di notifiche della shell", "Conflicts with the shell's system tray implementation": "In conflitto con l'area di notifica della shell", "When enabled keeps the content of the right sidebar loaded to reduce the delay when opening,\nat the cost of around 15MB of consistent RAM usage. Delay significance depends on your system's performance.\nUsing a custom kernel like linux-cachyos might help": "Mantiene il contenuto della barra laterale destra in memoria per ridurne il tempo di apertura,\nrichiede circa 15MB di RAM. Il tempo di apertura dipende dalle performance del tuo sistema.\nUsare un custom kernel come linux-cachyos può migliorare la velocità", "**Instructions**: Log into Mistral account, go to Keys on the sidebar, click Create new key": "**Istruzioni**: Accedi al tuo account <PERSON><PERSON>, vai nella sezione \"Keys\" dal menu laterale, clic<PERSON> \"Create new key\"", "Gives the model search capabilities (immediately)": "Abilita modalità ricerca del modello (immediatamente)", "Commands, edit configs, search.\nTakes an extra turn to switch to search mode if that's needed": "<PERSON><PERSON><PERSON>, modifica la richiesta, cerca.\nRichiede un turno in più per attivare la modalità ricerca se richiesta", "Online | %1's model | Delivers fast, responsive and well-formatted answers. Disadvantages: not very eager to do stuff; might make up unknown function calls": "Online | Modello di %1 | Ritorna risposte veloci e formattate correttamente. Svantaggi: poca voglia di svolgere compiti; potrebbe fare chiamate di funzioni inesistenti"}