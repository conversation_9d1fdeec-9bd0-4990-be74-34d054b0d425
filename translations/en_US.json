{"Mo": "Mo/*keep*/", "Tu": "Tu/*keep*/", "We": "We/*keep*/", "Th": "Th/*keep*/", "Fr": "Fr/*keep*/", "Sa": "Sa/*keep*/", "Su": "Su/*keep*/", "%1 characters": "%1 characters", "**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\n\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key": "**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\n\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key", "**Pricing**: free. Data used for training.\n\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key": "**Pricing**: free. Data used for training.\n\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key", ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!": ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!", "<i>No further instruction provided</i>": "<i>No further instruction provided</i>", "Action": "Action", "Add": "Add", "Add task": "Add task", "All-rounder | Good quality, decent quantity": "All-rounder | Good quality, decent quantity", "Allow NSFW": "Allow NSFW", "Allow NSFW content": "Allow NSFW content", "Anime": "Anime", "Anime boorus": "<PERSON>ime boorus", "App": "App", "Arrow keys to navigate, Enter to select\nEsc or click anywhere to cancel": "Arrow keys to navigate, Enter to select\nEsc or click anywhere to cancel", "Bluetooth": "Bluetooth", "Brightness": "Brightness", "Cancel": "Cancel", "Cheat sheet": "Cheat sheet", "Choose model": "Choose model", "Clean stuff | Excellent quality, no NSFW": "Clean stuff | Excellent quality, no NSFW", "Clear": "Clear", "Clear chat history": "Clear chat history", "Clear the current list of images": "Clear the current list of images", "Close": "Close", "Copy": "Copy", "Copy code": "Copy code", "Delete": "Delete", "Desktop": "Desktop", "Disable NSFW content": "Disable NSFW content", "Done": "Done", "Download": "Download", "Edit": "Edit", "Enter text to translate...": "Enter text to translate...", "Finished tasks will go here": "Finished tasks will go here", "For desktop wallpapers | Good quality": "For desktop wallpapers | Good quality", "For storing API keys and other sensitive information": "For storing API keys and other sensitive information", "Game mode": "Game mode", "Get the next page of results": "Get the next page of results", "Hibernate": "Hibernate", "Input": "Input", "Intelligence": "Intelligence", "Interface": "Interface", "Invalid arguments. Must provide `key` and `value`.": "Invalid arguments. Must provide `key` and `value`.", "Jump to current month": "Jump to current month", "Keep system awake": "Keep system awake", "Large images | God tier quality, no NSFW.": "Large images | God tier quality, no NSFW.", "Large language models": "Large language models", "Launch": "Launch", "Lock": "Lock", "Logout": "Logout", "Markdown test": "Markdown test", "Math result": "Math result", "No audio source": "No audio source", "No media": "No media", "No notifications": "No notifications", "Not visible to model": "Not visible to model", "Nothing here!": "Nothing here!", "Notifications": "Notifications", "OK": "OK", "Open file link": "Open file link", "Output": "Output", "Reboot": "Reboot", "Reboot to firmware settings": "Reboot to firmware settings", "Reload Hyprland & Quickshell": "Reload Hyprland & Quickshell", "Run": "Run", "Run command": "Run command", "Save": "Save", "Save to Downloads": "Save to Downloads", "Search": "Search", "Search the web": "Search the web", "Search, calculate or run": "Search, calculate or run", "Select Language": "Select Language", "Session": "Session", "Set API key": "Set API key", "Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.": "Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.", "Set the current API provider": "Set the current API provider", "Shutdown": "Shutdown", "Silent": "Silent", "Sleep": "Sleep", "System": "System", "Task Manager": "Task Manager", "Task description": "Task description", "Temperature must be between 0 and 2": "Temperature must be between 0 and 2", "The hentai one | Great quantity, a lot of NSFW, quality varies wildly": "The hentai one | Great quantity, a lot of NSFW, quality varies wildly", "The popular one | Best quantity, but quality can vary wildly": "The popular one | Best quantity, but quality can vary wildly", "Thinking": "Thinking", "Translation goes here...": "Translation goes here...", "Translator": "Translator", "Unfinished": "Unfinished", "Unknown": "Unknown", "Unknown Album": "Unknown Album", "Unknown Artist": "Unknown Artist", "Unknown Title": "Unknown Title", "View Markdown source": "View Markdown source", "Volume": "Volume", "Volume mixer": "Volume mixer", "Waifus only | Excellent quality, limited quantity": "Waifus only | Excellent quality, limited quantity", "Waiting for response...": "Waiting for response...", "Workspace": "Workspace", "Invalid API provider. Supported: \n-": "Invalid API provider. Supported: \n-", "Unknown command:": "Unknown command:", "Type /key to get started with online models\nCtrl+O to expand the sidebar\nCtrl+P to detach sidebar into a window": "Type /key to get started with online models\nCtrl+O to expand the sidebar\nCtrl+P to detach sidebar into a window", "Provider set to": "Provider set to", "Invalid model. Supported: \n```": "Invalid model. Supported: \n```", "That didn't work. Tips:\n- Check your tags and NSFW settings\n- If you don't have a tag in mind, type a page number": "That didn't work. Tips:\n- Check your tags and NSFW settings\n- If you don't have a tag in mind, type a page number", "Switched to search mode. Continue with the user's request.": "Switched to search mode. Continue with the user's request.", "Settings": "Settings", "Save chat": "Save chat", "Load chat": "Load chat", "or": "or", "Set the system prompt for the model.": "Set the system prompt for the model.", "To Do": "To Do", "Calendar": "Calendar", "Advanced": "Advanced", "About": "About", "Services": "Services", "Style": "Style", "Colors & Wallpaper": "Colors & Wallpaper", "Light": "Light", "Dark": "Dark", "Material palette": "Material palette", "Fidelity": "Fidelity", "Fruit Salad": "Fruit Salad", "Alternatively use /dark, /light, /img in the launcher": "Alternatively use /dark, /light, /img in the launcher", "Fake screen rounding": "Fake screen rounding", "When not fullscreen": "When not fullscreen", "Choose file": "Choose file", "Random SFW Anime wallpaper from Konachan\nImage is saved to ~/Pictures/Wallpapers": "Random SFW Anime wallpaper from Konachan\nImage is saved to ~/Pictures/Wallpapers", "Be patient...": "Be patient...", "Decorations & Effects": "Decorations & Effects", "Tonal Spot": "Tonal Spot", "Shell windows": "Shell windows", "Auto": "Auto", "Wallpaper": "Wallpaper", "Content": "Content", "Title bar": "Title bar", "Transparency": "Transparency", "Expressive": "Expressive", "Yes": "Yes", "Enable": "Enable", "Rainbow": "Rainbow", "Might look ass. Unsupported.": "Might look ass. Unsupported.", "Monochrome": "Monochrome", "Random: Konachan": "Random: <PERSON><PERSON><PERSON>", "Center title": "Center title", "Neutral": "Neutral", "Pick wallpaper image on your system": "Pick wallpaper image on your system", "No": "No", "AI": "AI", "Local only": "Local only", "Policies": "Policies", "Weeb": "<PERSON><PERSON>", "Closet": "Closet", "Show next time": "Show next time", "Usage": "Usage", "Plain rectangle": "Plain rectangle", "Useless buttons": "Useless buttons", "GitHub": "GitHub", "Style & wallpaper": "Style & wallpaper", "Configuration": "Configuration", "Change any time later with /dark, /light, /img in the launcher": "Change any time later with /dark, /light, /img in the launcher", "Keybinds": "Keybinds", "Float": "Float", "Hug": "<PERSON>g", "illogical-impulse Welcome": "illogical-impulse Welcome", "Info": "Info", "Volume limit": "Volume limit", "Prevents abrupt increments and restricts volume limit": "Prevents abrupt increments and restricts volume limit", "Resources": "Resources", "12h am/pm": "12h am/pm", "Base URL": "Base URL", "Audio": "Audio", "Networking": "Networking", "Format": "Format", "Time": "Time", "Battery": "Battery", "Prefixes": "Prefixes", "Emojis": "Emojis", "Earbang protection": "Earbang protection", "Automatically suspends the system when battery is low": "Automatically suspends the system when battery is low", "Automatic suspend": "Automatic suspend", "Suspend at": "Suspend at", "Max allowed increase": "Max allowed increase", "Web search": "Web search", "Polling interval (ms)": "Polling interval (ms)", "Clipboard": "Clipboard", "Low warning": "Low warning", "24h": "24h", "Use Levenshtein distance-based algorithm instead of fuzzy": "Use Levenshtein distance-based algorithm instead of fuzzy", "System prompt": "System prompt", "12h AM/PM": "12h AM/PM", "Could be better if you make a ton of typos,\nbut results can be weird and might not work with acronyms\n(e.g. \"GIMP\" might not give you the paint program)": "Could be better if you make a ton of typos,\nbut results can be weird and might not work with acronyms\n(e.g. \"GIMP\" might not give you the paint program)", "Critical warning": "Critical warning", "User agent (for services that require it)": "User agent (for services that require it)", "Such regions could be images or parts of the screen that have some containment.\nMight not always be accurate.\nThis is done with an image processing algorithm run locally and no AI is used.": "Such regions could be images or parts of the screen that have some containment.\nMight not always be accurate.\nThis is done with an image processing algorithm run locally and no AI is used.", "Note: turning off can hurt readability": "Note: turning off can hurt readability", "Workspaces shown": "Workspaces shown", "Dark/Light toggle": "Dark/Light toggle", "Dock": "Dock", "Weather": "Weather", "Pinned on startup": "Pinned on startup", "Tip: Hide icons and always show numbers for\nthe classic illogical-impulse experience": "Tip: Hide icons and always show numbers for\nthe classic illogical-impulse experience", "Always show numbers": "Always show numbers", "Buttons": "Buttons", "Keyboard toggle": "Keyboard toggle", "Scale (%)": "Scale (%)", "Overview": "Overview", "Rows": "Rows", "Borderless": "Borderless", "Screenshot tool": "Screenshot tool", "Number show delay when pressing Super (ms)": "Number show delay when pressing Super (ms)", "Timeout (ms)": "Timeout (ms)", "Show app icons": "Show app icons", "Workspaces": "Workspaces", "Columns": "Columns", "On-screen display": "On-screen display", "Screen snip": "Screen snip", "Mic toggle": "Mic toggle", "Hover to reveal": "Hover to reveal", "Bar": "Bar", "Show background": "Show background", "Show regions of potential interest": "Show regions of potential interest", "Color picker": "Color picker", "Help & Support": "Help & Support", "Discussions": "Discussions", "Color generation": "Color generation", "Dotfiles": "<PERSON><PERSON><PERSON>", "Distro": "Distro", "Privacy Policy": "Privacy Policy", "Documentation": "Documentation", "Shell & utilities theming must also be enabled": "Shell & utilities theming must also be enabled", "Ignored if terminal theming is not enabled": "Ignored if terminal theming is not enabled", "illogical-impulse": "illogical-impulse", "Donate": "Donate", "Terminal": "Terminal", "Shell & utilities": "Shell & utilities", "Qt apps": "Qt apps", "Force dark mode in terminal": "Force dark mode in terminal", "Report a Bug": "Report a Bug", "Issues": "Issues", "Drag or click a region • LMB: Copy • RMB: Edit": "Drag or click a region • LMB: Copy • RMB: Edit", "Current model: %1\nSet it with %2model MODEL": "Current model: %1\nSet it with %2model MODEL", "Message the model... \"%1\" for commands": "Message the model... \"%1\" for commands", "No API key set for %1": "No API key set for %1", "Loaded the following system prompt\n\n---\n\n%1": "Loaded the following system prompt\n\n---\n\n%1", "%1 | Right-click to configure": "%1 | Right-click to configure", "API key set for %1": "API key set for %1", "Online via %1 | %2's model": "Online via %1 | %2's model", "Current API endpoint: %1\nSet it with %2mode PROVIDER": "Current API endpoint: %1\nSet it with %2mode PROVIDER", "Go to source (%1)": "Go to source (%1)", "Temperature set to %1": "Temperature set to %1", "Enter tags, or \"%1\" for commands": "Enter tags, or \"%1\" for commands", "%1 queries pending": "%1 queries pending", "API key:\n\n```txt\n%1\n```": "API key:\n\n```txt\n%1\n```", "%1 Safe Storage": "%1 Safe Storage", "%1 does not require an API key": "%1 does not require an API key", "Temperature: %1": "Temperature: %1", "Model set to %1": "Model set to %1", "Page %1": "Page %1", "Local Ollama model | %1": "Local Ollama model | %1", "The current system prompt is\n\n---\n\n%1": "The current system prompt is\n\n---\n\n%1", "Unknown function call: %1": "Unknown function call: %1", "%1 notifications": "%1 notifications", "Load chat from %1": "Load chat from %1", "Load prompt from %1": "Load prompt from %1", "Save chat to %1": "Save chat to %1", "Weather Service": "Weather Service", "Cannot find a GPS service. Using the fallback method instead.": "Cannot find a GPS service. Using the fallback method instead.", "Critically low battery": "Critically low battery", "Select output device": "Select output device", "Code saved to file": "Code saved to file", "Online models disallowed\n\nControlled by `policies.ai` config option": "Online models disallowed\n\nControlled by `policies.ai` config option", "Scroll to change volume": "Scroll to change volume", "Elements": "Elements", "%1   •   %2 tasks": "%1   •   %2 tasks", "Download complete": "Download complete", "Please charge!\nAutomatic suspend triggers at %1": "Please charge!\nAutomatic suspend triggers at %1", "Cloudflare WARP": "Cloudflare WARP", "Cloudflare WARP (1.1.1.1)": "Cloudflare WARP (1.1.1.1)", "Scroll to change brightness": "Scroll to change brightness", "Connection failed. Please inspect manually with the <tt>warp-cli</tt> command": "Connection failed. Please inspect manually with the <tt>warp-cli</tt> command", "Select input device": "Select input device", "Registration failed. Please inspect manually with the <tt>warp-cli</tt> command": "Registration failed. Please inspect manually with the <tt>warp-cli</tt> command", "Consider plugging in your device": "Consider plugging in your device", "Low battery": "Low battery", "Saved to %1": "Saved to %1", "Sunset": "Sunset", "UV Index": "UV Index", "Humidity": "<PERSON><PERSON><PERSON><PERSON>", "Wind": "Wind", "Sunrise": "Sunrise", "Pressure": "Pressure", "Visibility": "Visibility", "Precipitation": "Precipitation", "Time to full:": "Time to full:", "Time to empty:": "Time to empty:", "Fully charged": "Fully charged", "Charging:": "Charging:", "Discharging:": "Discharging:", "No pending tasks": "No pending tasks", "... and %1 more": "... and %1 more", "Used:": "Used:", "Free:": "Free:", "Total:": "Total:", "Load:": "Load:", "High": "High", "Medium": "Medium", "Low": "Low", "To set an API key, pass it with the %4 command\n\nTo view the key, pass \"get\" with the command<br/>\n\n### For %1:\n\n**Link**: %2\n\n%3": "To set an API key, pass it with the %4 command\n\nTo view the key, pass \"get\" with the command<br/>\n\n### For %1:\n\n**Link**: %2\n\n%3", "Online | %1's model | Delivers fast, responsive and well-formatted answers. Disadvantages: not very eager to do stuff; might make up unknown function calls": "Online | %1's model | Delivers fast, responsive and well-formatted answers. Disadvantages: not very eager to do stuff; might make up unknown function calls", "Incorrect password": "Incorrect password", "Usage: %1load CHAT_NAME": "Usage: %1load CHAT_NAME", "Usage: %1tool TOOL_NAME": "Usage: %1tool TOOL_NAME", "Feels like %1": "Feels like %1", "Always": "Always", "Break": "Break", "Vertical": "Vertical", "Set the tool to use for the model.": "Set the tool to use for the model.", "Lap": "<PERSON><PERSON>", "Approve": "Approve", "No API key\nSet it with /key YOUR_API_KEY": "No API key\nSet it with /key YOUR_API_KEY", "Sidebars": "Sidebars", "Up %1": "Up %1", "Your package manager is running": "Your package manager is running", "Commands, edit configs, search.\nTakes an extra turn to switch to search mode if that's needed": "Commands, edit configs, search.\nTakes an extra turn to switch to search mode if that's needed", "Tool set to: %1": "Tool set to: %1", "Disable tools": "Disable tools", "Tray": "Tray", "Temperature\nChange with /temp VALUE": "Temperature\nChange with /temp VALUE", "Conflicts with the shell's notification implementation": "Conflicts with the shell's notification implementation", "Pause": "Pause", "Wallpaper parallax": "Wallpaper parallax", "Kill conflicting programs?": "Kill conflicting programs?", "🌿 Long break: %1 minutes": "🌿 Long break: %1 minutes", "Reject": "Reject", "Online | Google's model\nNewer model that's slower than its predecessor but should deliver higher quality answers": "Online | Google's model\nNewer model that's slower than its predecessor but should deliver higher quality answers", "Welcome app": "Welcome app", "Attach a file. Only works with Gemini.": "Attach a file. Only works with <PERSON>.", "Depends on workspace": "Depends on workspace", "Night Light | Right-click to toggle Auto mode": "Night Light | Right-click to toggle Auto mode", "Total token count\nInput: %1\nOutput: %2": "Total token count\nInput: %1\nOutput: %2", "Tint icons": "Tint icons", "Refreshing (manually triggered)": "Refreshing (manually triggered)", "Gives the model search capabilities (immediately)": "Gives the model search capabilities (immediately)", "Performance Profile toggle": "Performance Profile toggle", "Timer": "Timer", "**Pricing**: Free tier available with limited rates. See https://docs.github.com/en/billing/concepts/product-billing/github-models\n\n**Instructions**: Generate a GitHub personal access token with Models permission, then set as API key here\n\n**Note**: To use this you will have to set the temperature parameter to 1": "**Pricing**: Free tier available with limited rates. See https://docs.github.com/en/billing/concepts/product-billing/github-models\n\n**Instructions**: Generate a GitHub personal access token with Models permission, then set as API key here\n\n**Note**: To use this you will have to set the temperature parameter to 1", "Command rejected by user": "Command rejected by user", "Thought": "Thought", "Long break": "Long break", "Hi there! First things first...": "Hi there! First things first...", "Preferred wallpaper zoom (%)": "Preferred wallpaper zoom (%)", "Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.", "🔴 Focus: %1 minutes": "🔴 Focus: %1 minutes", "Enjoy! You can reopen the welcome app any time with <tt>Super+Shift+Alt+/</tt>. To open the settings app, hit <tt>Super+I</tt>": "Enjoy! You can reopen the welcome app any time with <tt>Super+Shift+Alt+/</tt>. To open the settings app, hit <tt>Super+I</tt>", "There might be a download in progress": "There might be a download in progress", "Start": "Start", "System uptime:": "System uptime:", "Keep right sidebar loaded": "Keep right sidebar loaded", "Current tool: %1\nSet it with %2tool TOOL": "Current tool: %1\nSet it with %2tool TOOL", "Corner style": "Corner style", "Reset": "Reset", "Stopwatch": "Stopwatch", "Usage: %1save CHAT_NAME": "Usage: %1save CHAT_NAME", "API key is set\nChange with /key YOUR_API_KEY": "API key is set\nChange with /key YOUR_API_KEY", "Resume": "Resume", "Enter password": "Enter password", "Overall appearance": "Overall appearance", "Horizontal": "Horizontal", "Config file": "Config file", "Pomodoro": "Pomodoro", "Tint app icons": "Tint app icons", "Invalid tool. Supported tools:\n- %1": "Invalid tool. Supported tools:\n- %1", "Invalid arguments. Must provide `command`.": "Invalid arguments. Must provide `command`.", "When enabled keeps the content of the right sidebar loaded to reduce the delay when opening,\nat the cost of around 15MB of consistent RAM usage. Delay significance depends on your system's performance.\nUsing a custom kernel like linux-cachyos might help": "When enabled keeps the content of the right sidebar loaded to reduce the delay when opening,\nat the cost of around 15MB of consistent RAM usage. Delay significance depends on your system's performance.\nUsing a custom kernel like linux-cachyos might help", "Depends on sidebars": "Depends on sidebars", "To Do:": "To Do:", "**Instructions**: Log into Mistral account, go to Keys on the sidebar, click Create new key": "**Instructions**: Log into Mistral account, go to Keys on the sidebar, click Create new key", "Online | Google's model\nFast, can perform searches for up-to-date information": "Online | Google's model\nFast, can perform searches for up-to-date information", "Place at the bottom/right": "Place at the bottom/right", "Bar layout": "Bar layout", "Focus": "Focus", "Conflicts with the shell's system tray implementation": "Conflicts with the shell's system tray implementation", "Shell conflicts killer": "Shell conflicts killer", "☕ Break: %1 minutes": "☕ Break: %1 minutes", "Open the shell config file.\nIf the button doesn't work or doesn't open in your favorite editor,\nyou can manually open ~/.config/illogical-impulse/config.json": "Open the shell config file.\nIf the button doesn't work or doesn't open in your favorite editor,\nyou can manually open ~/.config/illogical-impulse/config.json", "Online | Google's model\nGoogle's state-of-the-art multipurpose model that excels at coding and complex reasoning tasks.": "Online | Google's model\nGoogle's state-of-the-art multipurpose model that excels at coding and complex reasoning tasks.", "EasyEffects | Right-click to configure": "EasyEffects | Right-click to configure", "Automatically hide": "Automatically hide"}