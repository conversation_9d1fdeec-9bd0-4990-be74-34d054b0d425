{"Mo": "月/*keep*/", "Tu": "火/*keep*/", "We": "水/*keep*/", "Th": "木/*keep*/", "Fr": "金/*keep*/", "Sa": "土/*keep*/", "Su": "日/*keep*/", "%1 characters": "%1 文字", "**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\n\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key": "**料金**: 無料。データ利用ポリシーはOpenRouterアカウント設定によって異なります。\n\n**手順**: OpenRouterアカウントにログインし、右上メニューのKeysに進み、Create API Keyをクリックしてください。", "**Pricing**: free. Data used for training.\n\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key": "**料金**: 無料。データは学習に使用されます。\n\n**手順**: Googleアカウントにログインし、AI StudioにGoogle Cloudプロジェクトの作成などを許可し、戻ってAPIキーを取得してください。", ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!": "Zerochanの注意:\n- 色を入力する必要があります\n- `sidebar.booru.zerochan.username`設定でユーザー名を設定してください。設定しないと[利用停止になる場合があります](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)！", "<i>No further instruction provided</i>": "<i>追加の指示はありません</i>", "Action": "操作", "Add": "追加", "Add task": "タスクを追加", "All-rounder | Good quality, decent quantity": "万能型 | 高品質・十分な量", "Allow NSFW": "NSFWを許可", "Allow NSFW content": "NSFWコンテンツを許可", "Anime": "アニメ", "Anime boorus": "アニメ画像掲示板", "App": "アプリ", "Arrow keys to navigate, Enter to select\nEsc or click anywhere to cancel": "矢印キーで移動、Enterで選択\nEsc/クリックでキャンセル", "Bluetooth": "Bluetooth", "Brightness": "<PERSON>るさ", "Cancel": "キャンセル", "Cheat sheet": "チートシート", "Choose model": "モデルを選択", "Clean stuff | Excellent quality, no NSFW": "健全 | 高品質・NSFWなし", "Clear": "クリア", "Clear chat history": "チャット履歴を消去", "Clear the current list of images": "現在の画像リストをクリア", "Close": "閉じる", "Copy": "コピー", "Copy code": "コードをコピー", "Delete": "削除", "Desktop": "デスクトップ", "Disable NSFW content": "NSFWコンテンツを無効化", "Done": "完了", "Download": "ダウンロード", "Edit": "編集", "Enter text to translate...": "翻訳するテキストを入力...", "Finished tasks will go here": "完了したタスクはここに表示されます", "For desktop wallpapers | Good quality": "デスクトップ壁紙向け | 高品質", "For storing API keys and other sensitive information": "APIキーや機密情報の保存用", "Game mode": "ゲームモード", "Get the next page of results": "次のページを取得", "Hibernate": "休止", "Input": "入力", "Intelligence": "知能", "Interface": "インターフェース", "Invalid arguments. Must provide `key` and `value`.": "無効な引数です。`key`と`value`を指定してください。", "Jump to current month": "現在の月へ移動", "Keep system awake": "システムをスリープさせない", "Large images | God tier quality, no NSFW.": "大きな画像 | 最高品質・NSFWなし", "Large language models": "大規模言語モデル", "Launch": "起動", "Lock": "ロック", "Logout": "ログアウト", "Markdown test": "Markdownテスト", "Math result": "計算結果", "No audio source": "音声ソースなし", "No media": "メディアなし", "No notifications": "通知なし", "Not visible to model": "モデルには表示されません", "Nothing here!": "何もありません！", "Notifications": "通知", "OK": "OK", "Open file link": "ファイルリンクを開く", "Output": "出力", "Reboot": "再起動", "Reboot to firmware settings": "ファームウェア設定で再起動", "Reload Hyprland & Quickshell": "HyprlandとQuickshellを再読み込み", "Run": "実行", "Run command": "コマンドを実行", "Save": "保存", "Save to Downloads": "ダウンロードに保存", "Search": "検索", "Search the web": "ウェブ検索", "Search, calculate or run": "検索・計算・実行", "Select Language": "言語を選択", "Session": "セッション", "Set API key": "APIキーを設定", "Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.": "モデルの温度（ランダム性）を設定します。Geminiは0～2、他は0～1です。初期値は0.5です。", "Set the current API provider": "現在のAPIプロバイダーを設定します", "Shutdown": "シャットダウン", "Silent": "サイレント", "Sleep": "スリープ", "System": "システム", "Task Manager": "タスクマネージャー", "Task description": "タスクの説明", "Temperature must be between 0 and 2": "温度は0～2の間で指定してください", "The hentai one | Great quantity, a lot of NSFW, quality varies wildly": "成人向け | 量は多いが品質は様々・NSFW多数", "The popular one | Best quantity, but quality can vary wildly": "人気 | 量は最多だが品質は様々", "Thinking": "考え中", "Translation goes here...": "ここに翻訳が表示されます...", "Translator": "翻訳", "Unfinished": "未完了", "Unknown": "不明", "Unknown Album": "不明なアルバム", "Unknown Artist": "不明なアーティスト", "Unknown Title": "不明なタイトル", "View Markdown source": "Markdownソースを表示", "Volume": "音量", "Volume mixer": "ボリュームミキサー", "Waifus only | Excellent quality, limited quantity": "キャラクター | 高品質・量は少なめ", "Waiting for response...": "応答待ち...", "Workspace": "ワークスペース", "Invalid API provider. Supported: \n-": "無効なAPIプロバイダー。対応: \n-", "Unknown command:": "不明なコマンド:", "Type /key to get started with online models\nCtrl+O to expand the sidebar\nCtrl+P to detach sidebar into a window": "/key でオンラインモデルを開始\nCtrl+O でサイドバーを展開\nCtrl+P でサイドバーをウィンドウ化", "Provider set to": "プロバイダーを設定しました:", "Invalid model. Supported: \n```": "無効なモデル。対応: \n```", "That didn't work. Tips:\n- Check your tags and NSFW settings\n- If you don't have a tag in mind, type a page number": "うまくいきませんでした。ヒント:\n- タグやNSFW設定を確認\n- タグがなければページ番号を入力", "Switched to search mode. Continue with the user's request.": "検索モードに切り替えました。リクエストを続行します。", "Settings": "設定", "Save chat": "チャットを保存", "Load chat": "チャットを読み込み", "or": "または", "Set the system prompt for the model.": "モデルのシステムプロンプトを設定", "To Do": "やること", "Calendar": "カレンダー", "Advanced": "詳細", "About": "このアプリについて", "Services": "サービス", "Style": "スタイル", "Edit config": "設定を編集", "Colors & Wallpaper": "色と壁紙", "Light": "ライト", "Dark": "ダーク", "Material palette": "マテリアルパレット", "Fidelity": "忠実度", "Fruit Salad": "フルーツサラダ", "Alternatively use /dark, /light, /img in the launcher": "ランチャーで /dark, /light, /img も使用できます", "Fake screen rounding": "画面の角を丸める（疑似）", "When not fullscreen": "フルスクリーンでない時", "Choose file": "ファイルを選択", "Random SFW Anime wallpaper from Konachan\nImage is saved to ~/Pictures/Wallpapers": "Konachanから健全なアニメ壁紙をランダムで取得し、~/Pictures/Wallpapers に保存します", "Be patient...": "少々お待ちください…", "Decorations & Effects": "装飾と効果", "Tonal Spot": "トーナルスポット", "Shell windows": "シェルウィンドウ", "Auto": "自動", "Wallpaper": "壁紙", "Content": "コンテンツ", "Title bar": "タイトルバー", "Transparency": "透明度", "Expressive": "表現豊か", "Yes": "表示", "Enable": "有効化", "Rainbow": "レインボー", "Might look ass. Unsupported.": "表示が崩れる可能性があります（非推奨）", "Monochrome": "モノクロ", "Random: Konachan": "ランダム: <PERSON><PERSON><PERSON>", "Center title": "タイトルを中央に", "Neutral": "ニュートラル", "Pick wallpaper image on your system": "システムから壁紙画像を選択", "No": "非表示", "AI": "AI", "Local only": "ローカルのみ", "Policies": "ポリシー", "Weeb": "アニメファン向け", "Closet": "隠し", "Show next time": "次回も表示する", "Usage": "使用状況", "Plain rectangle": "長方形", "Useless buttons": "ダミーボタン", "GitHub": "GitHub", "Style & wallpaper": "スタイルと壁紙", "Configuration": "設定", "Change any time later with /dark, /light, /img in the launcher": "ランチャーで/dark, /light, /imgでいつでも変更可能", "Keybinds": "キー割り当て", "Float": "フローティング", "Hug": "固定", "illogical-impulse Welcome": "illogical-impulse へようこそ！", "Info": "情報", "Volume limit": "ボリューム制限", "Prevents abrupt increments and restricts volume limit": "急な音量変化を防ぎ、音量の上限を設定します", "Resources": "リソース", "12h am/pm": "12時間（AM/PM）", "Base URL": "ベースURL", "Audio": "オーディオ", "Networking": "ネットワーク", "Format": "フォーマット", "Time": "時間", "Battery": "バッテリー", "Prefixes": "接頭辞", "Emojis": "絵文字", "Earbang protection": "聴覚保護（急な大音量防止）", "Automatically suspends the system when battery is low": "バッテリー残量が少ないときに自動でスリープします", "Automatic suspend": "自動スリープ", "Suspend at": "スリープ開始残量（%）", "Max allowed increase": "音量の最大増加幅", "Web search": "ウェブ検索", "Polling interval (ms)": "ポーリング間隔（ms）", "Clipboard": "クリップボード", "Low warning": "バッテリー低下の警告", "24h": "24時間", "Use Levenshtein distance-based algorithm instead of fuzzy": "あいまい検索の代わりにレーベンシュタイン距離アルゴリズムを使用", "System prompt": "システムプロンプト", "12h AM/PM": "12時間（AM/PM）", "Could be better if you make a ton of typos,\nbut results can be weird and might not work with acronyms\n(e.g. \"GIMP\" might not give you the paint program)": "入力ミスが多い場合に便利ですが、結果が意図しないものになったり、略語（例：GIMP）が正しく検索できないことがあります", "Critical warning": "重大な警告", "User agent (for services that require it)": "ユーザーエージェント（必要なサービス用）", "Such regions could be images or parts of the screen that have some containment.\nMight not always be accurate.\nThis is done with an image processing algorithm run locally and no AI is used.": "これらの領域は、画像や画面の一部など、まとまりのある部分を指します。\n必ずしも正確ではありません。\nAIではなく、ローカルで実行される画像処理アルゴリズムによって検出されます。", "Note: turning off can hurt readability": "注意: オフにすると可読性が損なわれる場合があります", "Workspaces shown": "表示中のワークスペース", "Dark/Light toggle": "ダーク/ライト切替", "Dock": "ドック", "Weather": "天気", "Pinned on startup": "起動時にピン留め", "Tip: Hide icons and always show numbers for\nthe classic illogical-impulse experience": "ヒント: アイコンを非表示にして番号を常に表示すると、クラシックなillogical-impulseの使用感を体験できます", "Always show numbers": "数字を常に表示", "Buttons": "ボタン", "Keyboard toggle": "キーボード切替", "Scale (%)": "スケール（％）", "Overview": "概要", "Rows": "行数", "Borderless": "枠なし", "Screenshot tool": "スクリーンショットツール", "Number show delay when pressing Super (ms)": "Superキー押下時の数字表示遅延（ms）", "Timeout (ms)": "タイムアウト（ms）", "Show app icons": "アプリアイコンを表示", "Workspaces": "ワークスペース", "Columns": "列数", "On-screen display": "画面表示", "Screen snip": "画面の切り抜き", "Mic toggle": "マイク切替", "Hover to reveal": "ホバーで表示", "Bar": "バー", "Show background": "背景を表示", "Show regions of potential interest": "注目領域を表示", "Color picker": "カラーピッカー", "Help & Support": "ヘルプとサポート", "Discussions": "ディスカッション", "Color generation": "色の生成", "Dotfiles": "ドットファイル", "Distro": "ディストリビューション", "Privacy Policy": "プライバシーポリシー", "Documentation": "ドキュメント", "Shell & utilities theming must also be enabled": "「シェルとユーティリティ」のテーマ設定も有効にする必要があります", "illogical-impulse": "illogical-impulse", "Donate": "寄付", "Terminal": "ターミナル", "Shell & utilities": "シェルとユーティリティ", "Qt apps": "Qtアプリ", "Report a Bug": "バグを報告", "Issues": "課題", "Drag or click a region • LMB: Copy • RMB: Edit": "領域をドラッグまたはクリック • 左クリック: コピー • 右クリック: 編集", "Current model: %1\nSet it with %2model MODEL": "現在のモデル: %1\n%2model MODELで設定", "Message the model... \"%1\" for commands": "モデルにメッセージを送信... コマンドは「%1」", "No API key set for %1": "%1のAPIキーが設定されていません", "Loaded the following system prompt\n\n---\n\n%1": "次のシステムプロンプトを読み込みました\n\n---\n\n%1", "%1 | Right-click to configure": "%1 | 右クリックで設定", "API key set for %1": "%1のAPIキーを設定しました", "Online via %1 | %2's model": "%1経由オンライン | %2のモデル", "Current API endpoint: %1\nSet it with %2mode PROVIDER": "現在のAPIエンドポイント: %1\n%2mode PROVIDERで設定", "Go to source (%1)": "ソースを開く（%1）", "Temperature set to %1": "温度を%1に設定", "Enter tags, or \"%1\" for commands": "タグを入力、またはコマンドは「%1」", "%1 queries pending": "%1件のクエリが保留中", "API key:\n\n```txt\n%1\n```": "APIキー:\n\n```txt\n%1\n```", "%1 Safe Storage": "%1 セーフストレージ", "%1 does not require an API key": "%1はAPIキー不要です", "Temperature: %1": "温度: %1", "Model set to %1": "モデルを%1に設定", "Page %1": "ページ %1", "Local Ollama model | %1": "ローカルOllamaモデル | %1", "The current system prompt is\n\n---\n\n%1": "現在のシステムプロンプトは\n\n---\n\n%1", "Unknown function call: %1": "不明な関数呼び出し: %1", "%1 notifications": "%1件の通知", "Load chat from %1": "%1からチャットを読み込み", "Load prompt from %1": "%1からプロンプトを読み込み", "Save chat to %1": "チャットを%1に保存", "Weather Service": "天気サービス", "Cannot find a GPS service. Using the fallback method instead.": "GPSサービスが見つかりません。代替手段を使用します。", "Critically low battery": "バッテリー残量が非常に少ない", "Select output device": "出力デバイスを選択", "Code saved to file": "コードをファイルに保存しました", "Online models disallowed\n\nControlled by `policies.ai` config option": "オンラインモデルは許可されていません\n\n`policies.ai` 設定で管理されています", "Scroll to change volume": "スクロールで音量調整", "Elements": "要素", "%1   •   %2 tasks": "%1   •   %2件のタスク", "Download complete": "ダウンロード完了", "Please charge!\nAutomatic suspend triggers at %1": "充電してください！\n%1で自動的にサスペンドします", "Cloudflare WARP": "Cloudflare WARP", "Cloudflare WARP (1.1.1.1)": "Cloudflare WARP (1.1.1.1)", "Scroll to change brightness": "スクロールで明るさ調整", "Connection failed. Please inspect manually with the <tt>warp-cli</tt> command": "接続に失敗しました。<tt>warp-cli</tt> コマンドで手動確認してください", "Select input device": "入力デバイスを選択", "Registration failed. Please inspect manually with the <tt>warp-cli</tt> command": "登録に失敗しました。<tt>warp-cli</tt> コマンドで手動確認してください", "Consider plugging in your device": "電源を接続することを推奨します", "Low battery": "バッテリー残量低下", "Saved to %1": "%1に保存しました", "Sunset": "日没", "UV Index": "UV指数", "Humidity": "湿度", "Wind": "風", "Sunrise": "日の出", "Pressure": "気圧", "Visibility": "視界", "Precipitation": "降水量", "Time to full:": "満充電まで:", "Time to empty:": "空になるまで:", "Fully charged": "充電完了", "Charging:": "充電中:", "Discharging:": "放電中:", "No pending tasks": "保留中のタスクなし", "... and %1 more": "...他%1件", "Used:": "使用済み:", "Free:": "空き:", "Total:": "合計:", "Swap:": "スワップ:", "Not configured": "未設定", "Load:": "負荷:", "High": "高", "Medium": "中", "Low": "低", "Use the system file picker instead": "システム標準のファイル選択ツールを使用", "Tint icons": "アイコンに色付けする", "Connect to Wi-Fi": "Wi-Fiに接続", "Invalid arguments. Must provide `command`.": "無効な引数です。`command`を指定してください。", "System uptime:": "システム稼働時間:", "Gives the model search capabilities (immediately)": "モデルにすぐに検索能力を与えます", "Click to toggle light/dark mode (applied when wallpaper is chosen)": "クリックでライト/ダークモード切替（壁紙選択時に適用されます）", "**Pricing**: Free tier available with limited rates. See https://docs.github.com/en/billing/concepts/product-billing/github-models\n\n**Instructions**: Generate a GitHub personal access token with Models permission, then set as API key here\n\n**Note**: To use this you will have to set the temperature parameter to 1": "**料金**: 制限付きの無料枠があります。詳細は https://docs.github.com/en/billing/concepts/product-billing/github-models を参照\n\n**手順**: Modelsアクセス権を持つGitHub個人アクセストークンを生成し、ここでAPIキーとして設定してください\n\n**注意**: 使用するには温度パラメータを1に設定する必要があります", "Depends on workspace": "ワークスペースに依存", "Hi there! First things first...": "こんにちは！まずは始めましょう...", "Refreshing (manually triggered)": "更新中（手動で開始）", "Always": "常に", "No API key\nSet it with /key YOUR_API_KEY": "APIキーがありません\n/key YOUR_API_KEYで設定してください", "Usage: %1load CHAT_NAME": "使用法: %1load チャット名", "Sidebars": "サイドバー", "Search wallpapers": "壁紙を検索", "When this is off you'll have to click": "オフの場合、クリックで表示します", "Depends on sidebars": "サイドバーに依存", "Incorrect password": "パスワードが間違っています", "Current tool: %1\nSet it with %2tool TOOL": "現在のツール: %1\n%2tool TOOLで設定", "Overall appearance": "全体の外観", "To Do:": "やること:", "Region height": "領域の高さ", "Auto (System)": "自動（システム）", "Place the corners to trigger at the bottom": "トリガーとなる角を下部に配置", "Shell conflicts killer": "シェルの競合解消", "Enter password": "パスワードを入力", "☕ Break: %1 minutes": "☕ 休憩: %1分", "Reset": "リセット", "Connect": "接続", "Tint app icons": "アプリアイコンに淡い色付けをする", "Bar layout": "バーのレイアウト", "Conflicts with the shell's notification implementation": "シェルの通知機能と競合することがあります", "Corner open": "コーナー起動", "🌿 Long break: %1 minutes": "🌿 長い休憩: %1分", "Reject": "拒否", "Command rejected by user": "コマンドはユーザーによって拒否されました", "Start": "開始", "Brightness and volume": "明るさ・音量", "Corner style": "角のスタイル", "Total token count\nInput: %1\nOutput: %2": "トークン数合計\n入力: %1\n出力: %2", "No active player": "アクティブなプレーヤーがありません", "Performance Profile toggle": "パフォーマンスプロファイル切替", "Timer": "タイマー", "Conflicts with the shell's system tray implementation": "シェルのシステムトレイ機能と競合することがあります", "Online | %1's model | Delivers fast, responsive and well-formatted answers. Disadvantages: not very eager to do stuff; might make up unknown function calls": "オンライン | %1のモデル | 高速で反応が良く、整形された回答が得られます。欠点: あまり積極的でない場合があり、存在しない関数を提案することがあります", "Welcome app": "ようこそアプリ", "Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "オンライン | Googleモデル\nコスト効率と高スループットに最適化されたGemini 2.5 Flashモデル。", "Wallpaper parallax": "壁紙パララックス効果", "Place at the bottom": "下部に配置", "Invalid tool. Supported tools:\n- %1": "無効なツールです。対応ツール:\n- %1", "Password": "パスワード", "Details": "詳細", "Edit directory": "ディレクトリを編集", "Language": "言語", "Visualize region": "領域を可視化", "Enjoy! You can reopen the welcome app any time with <tt>Super+Shift+Alt+/</tt>. To open the settings app, hit <tt>Super+I</tt>": "お楽しみください！ウェルカムアプリは<tt>Super+Shift+Alt+/</tt>でいつでも開けます。設定アプリを開くには<tt>Super+I</tt>を押してください", "Online | Google's model\nGoogle's state-of-the-art multipurpose model that excels at coding and complex reasoning tasks.": "オンライン | Googleモデル\nコーディングや複雑な推論タスクに優れた、Googleの最先端多目的モデル。", "When enabled keeps the content of the right sidebar loaded to reduce the delay when opening,\nat the cost of around 15MB of consistent RAM usage. Delay significance depends on your system's performance.\nUsing a custom kernel like linux-cachyos might help": "有効にすると右サイドバーのコンテンツを常に読み込んでおくことで、表示遅延を短縮します。\n代償として約15MBのメモリを継続的に使用します。遅延の度合いはシステム性能に依存します。\nlinux-cachyosのようなカスタムカーネルの使用が効果的な場合があります。", "Place at bottom": "下部に配置", "Tool set to: %1": "ツールを設定: %1", "Set the tool to use for the model.": "モデルが使用するツールを設定します。", "Make sure your player has MPRIS support\nor try turning off duplicate player filtering": "お使いのプレイヤーがMPRISをサポートしているか確認するか、\n重複プレイヤーのフィルタリングを無効にしてみてください", "Select the language for the user interface.\n\"Auto\" will use your system's locale.": "インターフェース言語を選択します。\n「自動」を選ぶとシステムのロケールが使用されます。", "Value scroll": "スクロールで音量・明るさを調整", "There might be a download in progress": "ダウンロードが進行中のようです", "Approve": "承認", "Tray": "トレイ", "**Instructions**: Log into Mistral account, go to Keys on the sidebar, click Create new key": "**手順**: Mistralアカウントにログインし、サイドバーのKeysに進み、Create new keyをクリックしてください", "Pomodoro": "ポモドーロ", "Language setting saved. Please restart Quickshell (Ctrl+Super+R) to apply the new language.": "言語設定を保存しました。新しい言語を適用するにはQuickshellを再起動してください（Ctrl+Super+R）。", "Feels like %1": "体感温度 %1", "Commands, edit configs, search.\nTakes an extra turn to switch to search mode if that's needed": "コマンド、設定編集、検索が可能。\n検索モードへの切替が必要な場合は追加の対話が必要です", "Resume": "再開", "Preferred wallpaper zoom (%)": "壁紙の拡大率（%）", "Disable tools": "ツールをオフ", "Night Light | Right-click to toggle Auto mode": "ナイトライト | 右クリックで自動モード切替", "Online | Google's model\nFast, can perform searches for up-to-date information": "オンライン | Googleモデル\n高速で、最新情報を検索できます", "Hover to trigger": "ホバーでトリガー", "Keep right sidebar loaded": "右サイドバーを常に読み込む", "Kill conflicting programs?": "競合するプログラムを終了しますか？", "Pick a wallpaper": "壁紙を選択", "Online | Google's model\nNewer model that's slower than its predecessor but should deliver higher quality answers": "オンライン | Googleモデル\n旧モデルより低速ですが、より高品質な回答が期待できる新モデル", "Hit \"/\" to search": "「/」で検索", "Config file": "設定ファイル", "Attach a file. Only works with Gemini.": "ファイルを添付 (Geminiでのみ利用可能)", "To set an API key, pass it with the %4 command\n\nTo view the key, pass \"get\" with the command<br/>\n\n### For %1:\n\n**Link**: %2\n\n%3": "APIキーを設定するには、%4コマンドを使用してください\n\nキーを確認するには、コマンドに「get」を付けてください<br/>\n\n### %1について:\n\n**リンク**: %2\n\n%3", "Thought": "思考", "Long break": "長い休憩", "Temperature\nChange with /temp VALUE": "温度\n/temp 値で変更", "Usage: %1tool TOOL_NAME": "使用法: %1tool ツール名", "Pause": "一時停止", "Allows you to open sidebars by clicking or hovering screen corners regardless of bar position": "バーの配置に関わらず、画面コーナーのクリックまたはホバーでサイドバーを開けるようにします", "EasyEffects | Right-click to configure": "EasyEffects | 右クリックで設定", "Up %1": "%1稼働中", "Place at the bottom/right": "下部/右側に配置", "Focus": "集中", "Stopwatch": "ストップウォッチ", "Interface Language": "インターフェース言語", "Memory usage": "メモリ使用量", "Automatically hide": "自動的に隠す", "Break": "休憩", "Your package manager is running": "パッケージマネージャーが実行中です", "API key is set\nChange with /key YOUR_API_KEY": "APIキーが設定済み\n/key YOUR_API_KEYで変更できます", "Open the shell config file.\nIf the button doesn't work or doesn't open in your favorite editor,\nyou can manually open ~/.config/illogical-impulse/config.json": "シェルの設定ファイルを開きます。\nボタンが機能しない、または任意のエディタで開かない場合は、\n~/.config/illogical-impulse/config.json を手動で開いてください", "CPU usage": "CPU使用率", "Swap usage": "スワップ使用量", "Usage: %1save CHAT_NAME": "使用法: %1save チャット名", "🔴 Focus: %1 minutes": "🔴 集中: %1分", "Lap": "ラップ", "Horizontal": "水平", "Region width": "領域の幅", "Vertical": "垂直"}